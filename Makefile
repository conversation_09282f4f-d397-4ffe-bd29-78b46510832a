# 项目名称
APP_NAME=zlmUrlGenert

# Go命令
GO=go

# 源文件
SRC=main.go aes.go

# 目标平台: 默认目标为当前系统
.PHONY: all clean mac win

# 构建所有平台
all: mac win

# 本地Mac平台构建
mac:
	@echo "构建Mac版本..."
	$(GO) build -o $(APP_NAME) $(SRC)
	@echo "Mac版本构建完成: $(APP_NAME)"

# Windows AMD64平台构建
win:
	@echo "构建Windows AMD64版本..."
	GOOS=windows GOARCH=amd64 CGO_ENABLED=1 $(GO) build -o $(APP_NAME).exe $(SRC)
	@echo "Windows版本构建完成: $(APP_NAME).exe"

# 清理生成的文件
clean:
	@echo "清理生成的文件..."
	rm -f $(APP_NAME) $(APP_NAME).exe
	@echo "清理完成"
