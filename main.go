package main

import (
	"crypto/md5"
	"database/sql"
	"fmt"
	"log"
	"os"
	"strings"

	_ "github.com/mattn/go-sqlite3"
)

// 初始化SQLite3数据库
func initDB() (*sql.DB, error) {
	db, err := sql.Open("sqlite3", "./device_config.db")
	if err != nil {
		return nil, err
	}

	// 创建设备配置表
	createTableSQL := `
	CREATE TABLE IF NOT EXISTS device_config (
		device_id TEXT PRIMARY KEY,
		server_id TEXT,
		server_address TEXT,
		server_port INTEGER,
		username TEXT,
		password TEXT,
		description TEXT,
		is_legacy_data BOOLEAN DEFAULT FALSE,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)
	`
	_, err = db.Exec(createTableSQL)
	if err != nil {
		return nil, err
	}

	return db, nil
}

// 生成顺序设备ID (20位)，按照 "34020000001320000100", "34020000001320000200" 格式生成
func generateDeviceID(db *sql.DB) string {
	// 前缀基础部分
	prefixBase := "3402000000"
	// 最后三位数字，默认从132开始
	prefixNum := 132

	// 查询数据库中最大的设备ID
	var maxDeviceID string
	row := db.QueryRow("SELECT device_id FROM device_config WHERE is_legacy_data = FALSE ORDER BY device_id DESC LIMIT 1")
	err := row.Scan(&maxDeviceID)

	// 初始序号为1
	seqNum := 1
	if err == nil && len(maxDeviceID) == 20 {
		// 提取前缀中的三位数字
		prefixStr := maxDeviceID[10:13]
		fmt.Sscanf(prefixStr, "%d", &prefixNum)

		// 提取序号部分（倧14-18位）
		seqPart := maxDeviceID[13:18]
		fmt.Sscanf(seqPart, "%d", &seqNum)

		// 增加1，得到下一个序号
		seqNum += 1

		// 检查序号范围
		if seqNum > 99999 {
			// 如果序号超过了99999，增加前缀数字并重置序号
			prefixNum += 1
			seqNum = 1
		}
	}
	if prefixNum == 133 {
		fmt.Println("前缀数字已达到最大值，无法继续生成设备ID")
		os.Exit(1)
	}

	// 生成完整前缀
	prefix := fmt.Sprintf("%s%03d", prefixBase, prefixNum)

	// 格式化序号为5位数字，后面加00
	suffixComponent := fmt.Sprintf("%05d00", seqNum)

	// 生成完整设备ID
	deviceID := prefix + suffixComponent

	return deviceID
}

// 显示使用方法
func showUsage() {
	fmt.Println("用法：")
	fmt.Println("1. 手动指定设备ID：")
	fmt.Println("   ./zlmUrlGenert <deviceID> [description]")
	fmt.Println("2. 自动生成设备ID（必须提供描述）：")
	fmt.Println("   ./zlmUrlGenert -gen \"描述信息\"")
	fmt.Println("3. 列出所有设备：")
	fmt.Println("   ./zlmUrlGenert -list")
}

// 显示所有设备记录
func listAllDevices(db *sql.DB) error {
	// 查询数据库中的所有设备
	rows, err := db.Query("SELECT device_id, server_id, server_address, server_port, username, password, description FROM device_config")
	if err != nil {
		return err
	}
	defer rows.Close()

	fmt.Printf("数据库中的设备列表：\n")
	fmt.Printf("%-20s %-20s %-20s %-10s %-20s %-10s %s\n", "设备ID", "服务器ID", "服务器地址", "端口", "账号", "密码", "描述")
	fmt.Printf("%s\n", strings.Repeat("-", 110))

	counter := 0
	for rows.Next() {
		var devID, srvID, srvAddr, username, pwd, desc string
		var port int
		if err := rows.Scan(&devID, &srvID, &srvAddr, &port, &username, &pwd, &desc); err != nil {
			return err
		}
		fmt.Printf("%-20s %-20s %-20s %-10d %-20s %-10s %s\n", devID, srvID, srvAddr, port, username, pwd, desc)
		counter++
	}

	fmt.Printf("\n总计：%d 台设备\n", counter)
	return nil
}

// 处理设备配置保存和显示
func processDevice(db *sql.DB, deviceID, description, secret, serverID, serverAddress string, serverPort int) error {
	host := "https://media-mclz.funyond.com" // 播放地址的域名

	// 获取设备密码
	fmt.Println("设备ID：", deviceID)
	fmt.Println("密钥：", secret)

	sec, err := AesEncrypt(deviceID, secret)
	if err != nil {
		return err
	}
	// 对AES加密结果进行MD5加密
	m := md5.New()
	m.Write([]byte(sec))
	md5Sum := fmt.Sprintf("%x", m.Sum(nil)) // 32位MD5值
	password := md5Sum[:8]                  // 取前8位

	// 将设备信息保存到数据库
	_, err = db.Exec(
		"INSERT OR REPLACE INTO device_config (device_id, server_id, server_address, server_port, username, password, description) VALUES (?, ?, ?, ?, ?, ?, ?)",
		deviceID, serverID, serverAddress, serverPort, deviceID, password, description,
	)
	if err != nil {
		return err
	}

	// 打印输出设备配置信息
	fmt.Printf("配置信息已保存到数据库\n")
	fmt.Printf("服务器ID：%s\n", serverID)
	fmt.Printf("服务器地址：%s\n", serverAddress)
	fmt.Printf("服务器端口：%d\n", serverPort)
	fmt.Printf("设备ID：%s\n", deviceID)
	fmt.Printf("账号：%s\n", deviceID)
	fmt.Printf("密码：%s\n", password)
	fmt.Printf("描述：%s\n", description)

	// 生成通道信息
	fmt.Printf("\n通道信息（仅供参考，不保存到数据库）：\n")
	for i := 1; i <= 16; i++ {
		channelID := fmt.Sprintf("%s%02d", deviceID[:18], i)
		fmt.Printf("通道ID-%d：%s\n", i, channelID)
	}

	// 生成播放地址
	fmt.Printf("\n播放地址：\n")
	for i := 0; i <= 16; i++ {
		channelID := fmt.Sprintf("%s%02d", deviceID[:18], i)
		m := md5.New()
		m.Write([]byte(secret))
		m.Write([]byte(deviceID))
		m.Write([]byte(channelID))
		token := fmt.Sprintf("%x", m.Sum(nil))
		fmt.Printf("%s/index.html?channel=%s&token=%s\n", host, channelID, token)
	}
	return nil
}

func main() {
	host := "https://media-mclz.funyond.com"
	secret := "13cafc7e24f041089f10331d61e5f477"
	serverID := "34020000002000000001"
	serverAddress := "gb28181.funyond.com"
	serverPort := 5060

	// 初始化数据库
	db, err := initDB()
	if err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}
	defer db.Close()

	// 参数不足，显示使用方法
	if len(os.Args) < 2 {
		showUsage()
		return
	}

	// 检查是否是命令行参数
	switch os.Args[1] {
	case "-gen", "-generate":
		// 自动生成设备ID，需要描述信息
		if len(os.Args) < 3 {
			fmt.Println("错误：使用 -gen 参数时必须提供设备描述信息")
			return
		}
		deviceID := generateDeviceID(db)
		description := os.Args[2]
		err := processDevice(db, deviceID, description, secret, serverID, serverAddress, serverPort)
		if err != nil {
			log.Fatalf("处理设备信息失败: %v", err)
		}

	case "-list":
		// 列出所有设备
		err := listAllDevices(db)
		if err != nil {
			log.Fatalf("查询数据库失败: %v", err)
		}

	default:
		// 认为输入的是设备ID
		if len(os.Args[1]) == 20 {
			deviceID := os.Args[1]

			// 默认描述，用户可以自定义
			description := "设备配置信息"
			if len(os.Args) > 2 {
				description = os.Args[2]
			}

			err := processDevice(db, deviceID, description, secret, serverID, serverAddress, serverPort)
			if err != nil {
				log.Fatalf("处理设备信息失败: %v", err)
			}

			// 在处理完设备信息后显示所有设备列表
			err = listAllDevices(db)
			if err != nil {
				log.Fatalf("查询数据库失败: %v", err)
			}
		} else {
			// 原有的其他功能保留（生成播放地址等）
			fmt.Println("设备ID格式不正确，应为20位字符。仅生成播放地址：")
			for i := 1; i <= 16; i++ {
				m := md5.New()
				m.Write([]byte(secret))
				m.Write([]byte(os.Args[1]))
				m.Write([]byte(fmt.Sprintf("%d", i)))
				token := fmt.Sprintf("%x", m.Sum(nil))
				fmt.Printf("%s/index.html?device=%s&channel=%d&token=%s\n", host, os.Args[1], i, token)
			}
		}
	}
}
